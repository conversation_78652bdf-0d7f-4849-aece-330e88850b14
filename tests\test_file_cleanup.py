"""
Test script for file cleanup functionality.

This script tests the enhanced file cleanup mechanisms to ensure
temporary SEG-Y files can be properly deleted on Windows.
"""

import os
import sys
import tempfile
import logging
import time

# Add the parent directory to the path to import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.file_utils import safe_remove_file, safe_remove_directory, close_file_handle_safely
from utils.data_utils import SegyHeaderLoader

# Configure logging
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')


def test_safe_file_removal():
    """Test the safe file removal functionality."""
    print("Testing safe file removal...")
    
    # Create a temporary file
    with tempfile.NamedTemporaryFile(delete=False, suffix=".test") as tmp_file:
        tmp_file.write(b"Test content")
        tmp_file_path = tmp_file.name
    
    print(f"Created temporary file: {tmp_file_path}")
    
    # Test removal
    success = safe_remove_file(tmp_file_path)
    
    if success:
        print("✓ File removal successful")
        return True
    else:
        print("✗ File removal failed")
        return False


def test_segy_header_loader_cleanup():
    """Test SegyHeaderLoader file handle cleanup."""
    print("\nTesting SegyHeaderLoader cleanup...")
    
    # We need a real SEG-Y file for this test
    # For now, we'll just test the close method without a real file
    try:
        # Create a mock loader to test the close method
        loader = SegyHeaderLoader.__new__(SegyHeaderLoader)
        loader.segy_path = "test_path.sgy"
        loader.segyfile = None  # Mock closed state
        
        # Test close method
        loader.close()
        
        # Test is_closed method
        if loader.is_closed():
            print("✓ SegyHeaderLoader close method works correctly")
            return True
        else:
            print("✗ SegyHeaderLoader close method failed")
            return False
            
    except Exception as e:
        print(f"✗ Error testing SegyHeaderLoader: {e}")
        return False


def test_context_manager():
    """Test SegyHeaderLoader context manager functionality."""
    print("\nTesting SegyHeaderLoader context manager...")
    
    try:
        # Test context manager without real file
        loader = SegyHeaderLoader.__new__(SegyHeaderLoader)
        loader.segy_path = "test_path.sgy"
        loader.segyfile = None
        
        # Test context manager methods
        with loader:
            pass  # Should automatically close on exit
        
        if loader.is_closed():
            print("✓ Context manager works correctly")
            return True
        else:
            print("✗ Context manager failed to close file handle")
            return False
            
    except Exception as e:
        print(f"✗ Error testing context manager: {e}")
        return False


def test_file_locking_simulation():
    """Simulate file locking scenario and test retry mechanism."""
    print("\nTesting file locking retry mechanism...")
    
    # Create a temporary file
    with tempfile.NamedTemporaryFile(delete=False, suffix=".lock_test") as tmp_file:
        tmp_file.write(b"Lock test content")
        tmp_file_path = tmp_file.name
    
    print(f"Created file for lock test: {tmp_file_path}")
    
    # Open the file to simulate a lock (on Windows)
    try:
        with open(tmp_file_path, 'rb') as locked_file:
            # Try to remove while file is open (should retry and eventually succeed after we close it)
            print("File is open, testing removal...")
            
            # Start removal in a separate thread would be ideal, but for simplicity
            # we'll just test that the file exists and then close and remove
            if os.path.exists(tmp_file_path):
                print("File exists while open")
        
        # Now file should be closed, try removal
        success = safe_remove_file(tmp_file_path)
        
        if success:
            print("✓ File removal after closing successful")
            return True
        else:
            print("✗ File removal after closing failed")
            # Clean up manually if our function failed
            try:
                os.remove(tmp_file_path)
            except:
                pass
            return False
            
    except Exception as e:
        print(f"✗ Error in file locking test: {e}")
        # Clean up
        try:
            os.remove(tmp_file_path)
        except:
            pass
        return False


def main():
    """Run all tests."""
    print("Running file cleanup tests...\n")
    
    tests = [
        test_safe_file_removal,
        test_segy_header_loader_cleanup,
        test_context_manager,
        test_file_locking_simulation
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"✗ Test {test.__name__} failed with exception: {e}")
            results.append(False)
    
    print(f"\n\nTest Results:")
    print(f"Passed: {sum(results)}/{len(results)}")
    
    if all(results):
        print("🎉 All tests passed!")
        return 0
    else:
        print("❌ Some tests failed.")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
