"""
File utilities for robust file operations, especially on Windows.

This module provides utilities for safe file deletion and management,
with special handling for Windows file locking issues.
"""

import os
import time
import gc
import logging
import platform
from typing import Optional, Union
from pathlib import Path


def safe_remove_file(file_path: Union[str, Path], max_retries: int = 5, 
                    initial_delay: float = 0.1, max_delay: float = 2.0) -> bool:
    """
    Safely remove a file with retry logic for Windows file locking issues.
    
    This function handles the common Windows issue where files cannot be deleted
    immediately due to file handles being held by other processes or delayed
    garbage collection.
    
    Args:
        file_path: Path to the file to remove
        max_retries: Maximum number of retry attempts
        initial_delay: Initial delay between retries in seconds
        max_delay: Maximum delay between retries in seconds
        
    Returns:
        bool: True if file was successfully removed, False otherwise
    """
    file_path = Path(file_path)
    
    if not file_path.exists():
        logging.debug(f"File {file_path} does not exist, nothing to remove")
        return True
    
    # Force garbage collection to ensure any file handles are closed
    gc.collect()
    
    delay = initial_delay
    last_error = None
    
    for attempt in range(max_retries + 1):
        try:
            file_path.unlink()
            logging.info(f"Successfully removed file: {file_path}")
            return True
            
        except PermissionError as e:
            last_error = e
            if platform.system() == "Windows" and e.winerror == 32:
                # Windows Error 32: The process cannot access the file because 
                # it is being used by another process
                logging.debug(f"Attempt {attempt + 1}/{max_retries + 1}: "
                            f"File {file_path} is locked, retrying in {delay:.2f}s")
                
                if attempt < max_retries:
                    time.sleep(delay)
                    # Exponential backoff with jitter
                    delay = min(delay * 1.5, max_delay)
                    # Force another garbage collection
                    gc.collect()
                    continue
                else:
                    logging.warning(f"Failed to remove file {file_path} after "
                                  f"{max_retries + 1} attempts: {e}")
                    return False
            else:
                # Other permission errors, don't retry
                logging.warning(f"Permission error removing file {file_path}: {e}")
                return False
                
        except OSError as e:
            last_error = e
            logging.warning(f"OS error removing file {file_path}: {e}")
            return False
            
        except Exception as e:
            last_error = e
            logging.error(f"Unexpected error removing file {file_path}: {e}")
            return False
    
    # If we get here, all retries failed
    logging.warning(f"Could not remove file {file_path} after {max_retries + 1} attempts. "
                   f"Last error: {last_error}")
    return False


def safe_remove_directory(dir_path: Union[str, Path], max_retries: int = 3) -> bool:
    """
    Safely remove a directory and all its contents with retry logic.
    
    Args:
        dir_path: Path to the directory to remove
        max_retries: Maximum number of retry attempts
        
    Returns:
        bool: True if directory was successfully removed, False otherwise
    """
    import shutil
    
    dir_path = Path(dir_path)
    
    if not dir_path.exists():
        logging.debug(f"Directory {dir_path} does not exist, nothing to remove")
        return True
    
    # Force garbage collection
    gc.collect()
    
    for attempt in range(max_retries + 1):
        try:
            shutil.rmtree(dir_path)
            logging.info(f"Successfully removed directory: {dir_path}")
            return True
            
        except PermissionError as e:
            if platform.system() == "Windows" and attempt < max_retries:
                logging.debug(f"Attempt {attempt + 1}/{max_retries + 1}: "
                            f"Directory {dir_path} access denied, retrying...")
                time.sleep(0.5 * (attempt + 1))
                gc.collect()
                continue
            else:
                logging.warning(f"Permission error removing directory {dir_path}: {e}")
                return False
                
        except Exception as e:
            logging.error(f"Error removing directory {dir_path}: {e}")
            return False
    
    return False


def close_file_handle_safely(file_handle, description: str = "file handle") -> bool:
    """
    Safely close a file handle with error handling.
    
    Args:
        file_handle: File handle to close (must have a close() method)
        description: Description of the file handle for logging
        
    Returns:
        bool: True if successfully closed, False otherwise
    """
    if file_handle is None:
        return True
        
    try:
        if hasattr(file_handle, 'close'):
            file_handle.close()
            logging.debug(f"Successfully closed {description}")
            return True
        else:
            logging.warning(f"{description} does not have a close() method")
            return False
            
    except Exception as e:
        logging.warning(f"Error closing {description}: {e}")
        return False


def ensure_directory_exists(dir_path: Union[str, Path]) -> bool:
    """
    Ensure a directory exists, creating it if necessary.
    
    Args:
        dir_path: Path to the directory
        
    Returns:
        bool: True if directory exists or was created successfully
    """
    dir_path = Path(dir_path)
    
    try:
        dir_path.mkdir(parents=True, exist_ok=True)
        return True
    except Exception as e:
        logging.error(f"Error creating directory {dir_path}: {e}")
        return False


def get_temp_file_info(file_path: Union[str, Path]) -> dict:
    """
    Get information about a temporary file for debugging purposes.
    
    Args:
        file_path: Path to the file
        
    Returns:
        dict: File information including size, permissions, etc.
    """
    file_path = Path(file_path)
    
    info = {
        'exists': file_path.exists(),
        'path': str(file_path),
        'absolute_path': str(file_path.absolute())
    }
    
    if file_path.exists():
        try:
            stat = file_path.stat()
            info.update({
                'size': stat.st_size,
                'mode': oct(stat.st_mode),
                'is_file': file_path.is_file(),
                'is_dir': file_path.is_dir()
            })
        except Exception as e:
            info['stat_error'] = str(e)
    
    return info
