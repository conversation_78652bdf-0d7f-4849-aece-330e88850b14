
import segyio
import numpy as np
import pandas as pd
import logging
from tqdm import tqdm

# Import tkinter conditionally
try:
    import tkinter as tk
    # --- Ensure a global Tk root exists for dialogs ---
    try:
        root = tk.Tk()
        root.withdraw()
    except Exception as e:
        print(f"Warning: Could not initialize tkinter root: {e}")
except ImportError:
    print("Warning: tkinter not available. GUI dialogs will not work.")

# SEG-Y File Merging Utility
def merge_segy_batch_files(batch_files, output_file):
    """
    Merge multiple SEG-Y batch files into a single SEG-Y file.

    This function combines multiple SEG-Y files (typically from batch processing)
    into a single consolidated SEG-Y file while preserving header information.
    It verifies compatibility between files and handles trace concatenation.

    Args:
        batch_files: List of batch SEG-Y file paths to merge
        output_file: Path to the output merged SEG-Y file

    Returns:
        str: Merge log message describing the operation result
    """
    if not batch_files:
        error_msg = "No batch files provided for merging."
        print(error_msg)
        return error_msg

    print(f"Merging {len(batch_files)} batch files into {output_file}...")

    # Handle single batch file case
    if len(batch_files) == 1:
        try:
            import shutil
            shutil.copy(batch_files[0], output_file)
            success_msg = f"Single batch file copied to {output_file}"
            print(success_msg)
            return success_msg
        except Exception as e:
            error_msg = f"Error copying single batch file: {e}"
            print(error_msg)
            return error_msg

    # Get total trace count and ensure all files have compatible specifications
    total_traces = 0
    specs = None

    for batch_file in batch_files:
        try:
            with segyio.open(batch_file, 'r', ignore_geometry=True) as f:
                if specs is None:
                    # Store specifications from first file
                    specs = segyio.tools.metadata(f)
                else:
                    # Verify compatibility with first file
                    new_specs = segyio.tools.metadata(f)
                    if (len(new_specs.samples) != len(specs.samples) or
                        not np.array_equal(new_specs.samples, specs.samples)):
                        error_msg = f"Error: Incompatible sample specifications in {batch_file}"
                        print(error_msg)
                        return error_msg

                # Add trace count from this file
                file_traces = f.tracecount
                total_traces += file_traces
        except Exception as e:
            error_msg = f"Error reading batch file {batch_file}: {e}"
            print(error_msg)
            return error_msg

    if specs is None:
        error_msg = "Could not obtain specifications from batch files."
        print(error_msg)
        return error_msg

    # Set the trace count for the merged file
    specs.tracecount = total_traces

    try:
        with segyio.create(output_file, specs) as dst:
            # Copy binary header from first file
            with segyio.open(batch_files[0], 'r', ignore_geometry=True) as src:
                dst.bin = src.bin

                # Copy text header if available
                try:
                    dst.text[0] = src.text[0]
                except:
                    pass

            # Copy traces and headers from all batch files
            dst_trace_index = 0
            for batch_file in tqdm(batch_files, desc=f"Merging batch files"):
                with segyio.open(batch_file, 'r', ignore_geometry=True) as src:
                    for src_trace_index in range(src.tracecount):
                        dst.header[dst_trace_index] = src.header[src_trace_index]
                        dst.trace[dst_trace_index] = src.trace[src_trace_index]
                        dst_trace_index += 1

        success_msg = f"Successfully merged {len(batch_files)} batch files into {output_file} with {total_traces} traces."
        print(success_msg)
        return success_msg

    except Exception as e:
        error_msg = f"Error during merge: {e}"
        print(error_msg)
        return error_msg

# SEG-Y Header Management
class SegyHeaderLoader:
    """
    Handles loading and processing of SEG-Y file headers.

    This class extracts header information from SEG-Y files, including inline/crossline
    numbers and coordinate data. It provides functionality to customize header byte
    locations and scaling factors.
    """
    def __init__(self, segy_path):
        """Initialize with path to SEG-Y file and prepare for header loading.

        Besides storing the provided path, we keep a persistent read‐only
        `segyio.SegyFile` handle so that other parts of the application (e.g.
        the export routines) can access survey metadata, headers and sample
        arrays through `header_loader.segyfile`.

        IMPORTANT: The file handle is opened in read-only mode with
        `ignore_geometry=True` to avoid the heavy geometry parsing overhead.
        We also add a small destructor so the handle is closed gracefully when
        the object is garbage-collected.
        Args:
            segy_path: Path to the SEG-Y file
        """
        self.segy_path = segy_path
        self.source_file_path = self.segy_path  # Alias kept for backward compatibility

        # Open a persistent segyio file handle for later use (needed by export)
        try:
            self.segyfile = segyio.open(self.segy_path, "r", ignore_geometry=True)
        except Exception as e:
            # Fallback: store None and warn; downstream code should handle this
            print(f"Warning: Could not open SEG-Y file '{self.segy_path}' early: {e}")
            self.segyfile = None

    def close(self):
        """
        Explicitly close the SEG-Y file handle.

        This method should be called when the SegyHeaderLoader is no longer needed
        to ensure proper cleanup of file resources, especially important on Windows
        to avoid file locking issues.
        """
        if hasattr(self, "segyfile") and self.segyfile is not None:
            try:
                self.segyfile.close()
                logging.debug(f"Closed SEG-Y file handle for {self.segy_path}")
            except Exception as e:
                logging.warning(f"Error closing SEG-Y file handle for {self.segy_path}: {e}")
            finally:
                self.segyfile = None

    def is_closed(self):
        """
        Check if the SEG-Y file handle is closed.

        Returns:
            bool: True if the file handle is closed or None, False if it's open
        """
        return self.segyfile is None

    def __enter__(self):
        """Context manager entry."""
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit - ensures file handle is closed."""
        self.close()

    def __del__(self):
        """Ensure that the persistent segyfile handle is closed when the loader is destroyed."""
        self.close()

    def load_headers(self, inline_byte, crossline_byte, x_coord_byte, y_coord_byte, scaler_byte, use_custom_scaler=False):
        """
        Load headers using provided byte positions.

        Args:
            inline_byte: Byte position for inline number
            crossline_byte: Byte position for crossline number
            x_coord_byte: Byte position for X coordinate
            y_coord_byte: Byte position for Y coordinate
            scaler_byte: Either the byte position for scaler or a custom scaler value
            use_custom_scaler: If True, scaler_byte is treated as a custom scaler value
                              If False, scaler_byte is treated as a byte position
        """
        self.inline_byte = inline_byte
        self.crossline_byte = crossline_byte
        self.x_coord_byte = x_coord_byte
        self.y_coord_byte = y_coord_byte
        self.scaler_byte = scaler_byte
        self.use_custom_scaler = use_custom_scaler

        with segyio.open(self.segy_path, 'r', ignore_geometry=True) as segyfile:
            print("\nLoading SEG-Y header information...")

            # Extract header values with progress bars
            self.inlines = np.array([h[self.inline_byte] for h in tqdm(segyfile.header, desc="Loading inlines")])
            self.crosslines = np.array([h[self.crossline_byte] for h in tqdm(segyfile.header, desc="Loading crosslines")])

            # Store the original inline/crossline ranges before filtering
            self.original_inline_min = int(np.min(self.inlines))
            self.original_inline_max = int(np.max(self.inlines))
            self.original_xline_min = int(np.min(self.crosslines))
            self.original_xline_max = int(np.max(self.crosslines))

            # Convert coordinates to float64 to avoid type casting issues during scaling
            self.x_coords = np.array([float(h[self.x_coord_byte]) for h in tqdm(segyfile.header, desc="Loading X coordinates")], dtype=np.float64)
            self.y_coords = np.array([float(h[self.y_coord_byte]) for h in tqdm(segyfile.header, desc="Loading Y coordinates")], dtype=np.float64)

            # Apply coordinate scaling factor
            try:
                if use_custom_scaler:
                    # Use the provided custom scaler value directly
                    custom_scaler = float(self.scaler_byte)
                    if custom_scaler >= 0:
                        self.x_coords *= custom_scaler
                        self.y_coords *= custom_scaler
                    else:
                        self.x_coords /= abs(custom_scaler)
                        self.y_coords /= abs(custom_scaler)
                    print(f"Applied custom coordinate scaler: {custom_scaler}")
                else:
                    # Read the scaler from the SEG-Y header at the specified byte position
                    scalers = np.array([h[self.scaler_byte] for h in tqdm(segyfile.header, desc="Loading coordinate scalers")])
                    # Use the first non-zero scaler value (or 1 if all are zero)
                    scaler_value = float(next((s for s in scalers if s != 0), 1))

                    if scaler_value >= 0:
                        self.x_coords *= scaler_value
                        self.y_coords *= scaler_value
                    else:
                        self.x_coords /= abs(scaler_value)
                        self.y_coords /= abs(scaler_value)
                    print(f"Applied coordinate scaler from byte {self.scaler_byte}: {scaler_value}")
            except Exception as e:
                print(f"Warning: Error applying coordinate scaling: {e}")
                print("Continuing with unscaled coordinates.")

        # Identify unique trace locations (handle duplicate coordinates)
        xy = np.column_stack((self.x_coords, self.y_coords))
        _, unique_idx = np.unique(xy, axis=0, return_index=True)
        self.unique_indices = unique_idx

        # Filter arrays to keep only unique trace locations
        self.x_coords = self.x_coords[unique_idx]
        self.y_coords = self.y_coords[unique_idx]
        self.inlines = self.inlines[unique_idx]
        self.crosslines = self.crosslines[unique_idx]

    def get_inline_crossline_range(self):
        """
        Get the full range of inline and crossline values from the SEG-Y file.

        Returns:
            dict: Dictionary containing min/max values for inlines and crosslines
        """
        try:
            if hasattr(self, 'original_inline_min'):
                # Return the original ranges stored during loading
                return {
                    'inline_min': self.original_inline_min,
                    'inline_max': self.original_inline_max,
                    'xline_min': self.original_xline_min,
                    'xline_max': self.original_xline_max
                }
            elif hasattr(self, 'inlines') and hasattr(self, 'crosslines'):
                # Fallback to the filtered ranges
                return {
                    'inline_min': int(np.min(self.inlines)) if len(self.inlines) > 0 else 0,
                    'inline_max': int(np.max(self.inlines)) if len(self.inlines) > 0 else 0,
                    'xline_min': int(np.min(self.crosslines)) if len(self.crosslines) > 0 else 0,
                    'xline_max': int(np.max(self.crosslines)) if len(self.crosslines) > 0 else 0
                }
            else:
                # If neither original nor filtered ranges are available, return zeros
                print("Warning: No inline/crossline data available in header loader")
                return {
                    'inline_min': 0,
                    'inline_max': 0,
                    'xline_min': 0,
                    'xline_max': 0
                }
        except Exception as e:
            # Catch any exceptions and return a valid dictionary with zeros
            print(f"Error retrieving inline/crossline ranges: {e}")
            return {
                'inline_min': 0,
                'inline_max': 0,
                'xline_min': 0,
                'xline_max': 0
            }

# Well Data Management Functions
def load_excel_data(excel_file_path):
    """
    Load well marker data from Excel file.

    This function loads well marker data from an Excel file and validates
    that it contains the required columns for well-seismic integration.

    Args:
        excel_file_path: Path to Excel file containing well marker data

    Returns:
        pandas.DataFrame: DataFrame containing well marker data

    Raises:
        ValueError: If required columns are missing from the Excel file
    """
    df = pd.read_excel(excel_file_path)
    expected_cols = ["X", "Y", "Z", "MD", "Surface", "Well"]
    for col in expected_cols:
        if col not in df.columns:
            raise ValueError(f"Missing expected column '{col}' in Excel file.")
    return df

def select_surfaces(df, title="Select Surfaces"):
    """
    Display a dialog for selecting geological surfaces from well data.

    This function creates a GUI dialog with a multi-select listbox allowing
    users to choose which geological surfaces to include in the analysis.

    Args:
        df: DataFrame containing well marker data
        title: Title for the selection window

    Returns:
        list: List of selected surface names
    """
    if 'tk' not in globals():
        raise ImportError("tkinter is not available. Cannot display selection dialog.")

    unique_surfaces = sorted(df["Surface"].unique().astype(str))

    if 'root' not in globals():
        # Create a temporary root if needed
        temp_root = tk.Tk()
        temp_root.withdraw()
        selection_window = tk.Toplevel(temp_root)
    else:
        selection_window = tk.Toplevel(root)

    selection_window.title(title)
    selection_window.geometry("300x400")
    tk.Label(selection_window, text="Select surface(s) to include:").pack(pady=5)
    frame = tk.Frame(selection_window)
    frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
    scrollbar = tk.Scrollbar(frame, orient="vertical")
    listbox = tk.Listbox(frame, selectmode=tk.MULTIPLE, yscrollcommand=scrollbar.set, width=40, height=15)
    scrollbar.config(command=listbox.yview)
    scrollbar.pack(side="right", fill="y")
    listbox.pack(side="left", fill="both", expand=True)
    for surf in unique_surfaces:
        listbox.insert(tk.END, surf)
    selected_indices = []
    def on_ok():
        nonlocal selected_indices
        selected_indices = listbox.curselection()
        selection_window.destroy()
    tk.Button(selection_window, text="OK", command=on_ok).pack(pady=10)
    selection_window.wait_window()

    if 'temp_root' in locals():
        temp_root.destroy()

    return [unique_surfaces[i] for i in selected_indices]

def select_well_marker_pairs(df_wells):
    """
    Display a dialog for selecting well-marker pairs for sample trace analysis.

    Args:
        df_wells: DataFrame containing well marker data

    Returns:
        list: List of selected well-marker pairs as DataFrame rows
    """
    if 'tk' not in globals():
        raise ImportError("tkinter is not available. Cannot display selection dialog.")

    well_marker_list = [f"{row['Well']} - {row['Surface']}" for _, row in df_wells.iterrows()]

    if 'root' not in globals():
        # Create a temporary root if needed
        temp_root = tk.Tk()
        temp_root.withdraw()
        selection_window = tk.Toplevel(temp_root)
    else:
        selection_window = tk.Toplevel(root)

    selection_window.title("Select Well-Marker Pairs for Sample Trace")
    selection_window.geometry("400x450")  # Increased height to fit filter controls

    # Add a frame for the filter controls
    filter_frame = tk.Frame(selection_window)
    filter_frame.pack(fill=tk.X, padx=10, pady=5)

    tk.Label(filter_frame, text="Filter by Surface:").pack(side=tk.LEFT)
    filter_entry = tk.Entry(filter_frame)
    filter_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5)

    # Initialize displayed indices with all items
    displayed_indices = list(range(len(well_marker_list)))

    def apply_filter():
        nonlocal displayed_indices
        keyword = filter_entry.get().strip().lower()
        if keyword:
            # Filter by surface (second part after " - ")
            displayed_indices = [
                i for i, item in enumerate(well_marker_list)
                if keyword in item.split(" - ")[1].lower()
            ]
        else:
            # Show all items if keyword is empty
            displayed_indices = list(range(len(well_marker_list)))
        # Update listbox
        listbox.delete(0, tk.END)
        for idx in displayed_indices:
            listbox.insert(tk.END, well_marker_list[idx])

    def clear_filter():
        filter_entry.delete(0, tk.END)
        apply_filter()

    # Add filter and clear buttons
    tk.Button(filter_frame, text="Filter", command=apply_filter).pack(side=tk.LEFT, padx=5)
    tk.Button(filter_frame, text="Clear Filter", command=clear_filter).pack(side=tk.LEFT, padx=5)

    # Instruction label
    tk.Label(selection_window, text="Select well-marker pair(s) for sample trace:").pack(pady=5)

    # Frame for listbox and scrollbar
    frame = tk.Frame(selection_window)
    frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

    scrollbar = tk.Scrollbar(frame, orient="vertical")
    listbox = tk.Listbox(frame, selectmode=tk.MULTIPLE, yscrollcommand=scrollbar.set, width=50, height=15)
    scrollbar.config(command=listbox.yview)
    scrollbar.pack(side="right", fill="y")
    listbox.pack(side="left", fill="both", expand=True)

    # Initially populate the listbox with all items
    for idx in displayed_indices:
        listbox.insert(tk.END, well_marker_list[idx])

    selected_indices = []
    def on_ok():
        nonlocal selected_indices
        selected_positions = [int(x) for x in listbox.curselection()]
        # Map listbox positions to original indices
        selected_indices = [displayed_indices[pos] for pos in selected_positions]
        selection_window.destroy()

    tk.Button(selection_window, text="OK", command=on_ok).pack(pady=10)
    selection_window.wait_window()

    if 'temp_root' in locals():
        temp_root.destroy()

    return [df_wells.iloc[i] for i in selected_indices]

def get_nearest_trace_index(header_loader, well_x, well_y):
    """
    Find the nearest seismic trace index to a given X,Y coordinate.

    This function calculates the Euclidean distance from a specified point
    to all trace locations and returns the index of the closest trace.
    Used for correlating well locations with seismic data.

    Args:
        header_loader: SegyHeaderLoader object containing trace coordinates
        well_x: X-coordinate of the point (typically a well location)
        well_y: Y-coordinate of the point

    Returns:
        int: Index of the nearest trace in the SEG-Y file
    """
    # Stack X and Y coordinates for vectorized distance calculation
    coords = np.column_stack((header_loader.x_coords, header_loader.y_coords))

    # Calculate Euclidean distances from the point to all trace locations
    distances = np.linalg.norm(coords - np.array([well_x, well_y]), axis=1)

    # Return the trace index with the minimum distance
    return header_loader.unique_indices[np.argmin(distances)]

def get_surfaces(df):
    """
    Get unique surfaces from well data.

    This function returns the unique surfaces from the DataFrame.

    Args:
        df: DataFrame containing well marker data

    Returns:
        list: List of unique surface names
    """
    return sorted(df["Surface"].unique().astype(str))

def get_well_marker_pairs(df_wells):
    """
    Get well-marker pairs from well data.

    This function returns the well-marker pairs from the DataFrame.

    Args:
        df_wells: DataFrame containing well marker data

    Returns:
        dict: Dictionary mapping display strings to DataFrame indices
    """
    well_marker_dict = {}
    for idx, row in df_wells.iterrows():
        display_str = f"{row['Well']} - {row['Surface']}"
        well_marker_dict[display_str] = idx
    return well_marker_dict



def load_trace_sample(segy_path, trace_index):
    """
    Load a single trace from a SEG-Y file.

    This function opens a SEG-Y file and extracts the amplitude data
    for a specific trace index.

    Args:
        segy_path: Path to the SEG-Y file
        trace_index: Index of the trace to load

    Returns:
        numpy.ndarray: Amplitude data for the requested trace
    """
    try:
        with segyio.open(segy_path, 'r', ignore_geometry=True) as segyfile:
            # Check if trace_index is valid
            if trace_index < 0 or trace_index >= segyfile.tracecount:
                print(f"Warning: Trace index {trace_index} out of range (0-{segyfile.tracecount-1})")
                return np.zeros(len(segyfile.samples))

            try:
                # Try to get the trace directly
                trace_data = segyfile.trace[trace_index]

                # Check if we got a valid trace
                if hasattr(trace_data, 'shape') or hasattr(trace_data, '__len__'):
                    return trace_data
                else:
                    print(f"Warning: Trace data has unexpected type: {type(trace_data)}")
                    # Try to create a trace from samples
                    return np.zeros(len(segyfile.samples))
            except Exception as e:
                print(f"Error accessing trace {trace_index}: {e}")
                # Return zeros with the correct length
                return np.zeros(len(segyfile.samples))
    except Exception as e:
        print(f"Error opening SEG-Y file: {e}")
        # Return a default empty trace
        return np.zeros(1000)  # Default size if we can't determine

def get_sampling_interval(segy_path):
    """
    Extract the time sampling interval from a SEG-Y file.

    This function reads the binary header of a SEG-Y file to determine
    the time sampling interval (dt) in seconds. If the header cannot be read,
    it falls back to a default value of 4 ms.

    Args:
        segy_path: Path to the SEG-Y file

    Returns:
        float: Sampling interval in seconds
    """
    try:
        with segyio.open(segy_path, 'r', ignore_geometry=True) as segyfile:
            # Convert microseconds to seconds
            dt = segyfile.bin[segyio.BinField.Interval] / 1_000_000
        print(f"Sampling interval (dt): {dt} seconds")
        return dt
    except Exception as e:
        print(f"Error retrieving sampling interval: {e}")
        # Fall back to default 4 ms sampling rate
        return 0.004

def get_trace_count(segy_path):
    """
    Get the actual number of traces in a SEG-Y file by identifying the array that is not time samples.

    This function uses the sampling rate information from the SEGY file to determine
    which dimension in the data represents traces vs. time samples.

    Args:
        segy_path: Path to the SEG-Y file

    Returns:
        int: The number of traces in the SEG-Y file
    """
    try:
        with segyio.open(segy_path, 'r', ignore_geometry=True) as segyfile:
            # Get the trace count directly from segyio
            trace_count = segyfile.tracecount

            # Get the number of samples per trace
            num_samples = len(segyfile.samples)

            # Get the sampling interval in seconds
            dt = segyfile.bin[segyio.BinField.Interval] / 1_000_000

            # Get a sample trace to verify dimensions
            if trace_count > 0:
                try:
                    # Try to get the first trace as a numpy array
                    sample_trace = segyfile.trace[0]

                    # Check if sample_trace has a length
                    if hasattr(sample_trace, '__len__'):
                        sample_length = len(sample_trace)
                        if sample_length != num_samples:
                            print(f"Warning: Sample trace length ({sample_length}) doesn't match expected samples ({num_samples})")
                    else:
                        print(f"Warning: Sample trace doesn't have a length attribute. Type: {type(sample_trace)}")
                        sample_length = num_samples  # Assume it matches
                except Exception as trace_e:
                    print(f"Warning: Could not access first trace: {trace_e}")
                    sample_length = num_samples  # Assume it matches

                # Calculate expected trace duration based on sampling rate
                expected_duration = num_samples * dt
                print(f"Expected trace duration: {expected_duration:.3f} seconds")

                # Verify that the trace count is not the time samples array
                # by checking if the trace count is reasonable compared to the sampling rate
                if trace_count < num_samples and num_samples * dt > 0.1:  # Typical seismic trace is at least 0.1 seconds
                    print(f"Verified trace count ({trace_count}) is not the time samples array ({num_samples})")
                else:
                    print(f"Note: Trace count ({trace_count}) might be confused with time samples ({num_samples})")

            print(f"SEG-Y file contains {trace_count} traces with {num_samples} samples per trace")
            return trace_count
    except Exception as e:
        print(f"Error determining trace count: {e}")
        return 0

# NumPy Data Classes for Enhanced Data Format Support
def validate_numpy_data(data, expected_dimensions):
    """
    Validate NumPy array structure and content for seismic data.
    
    Args:
        data: NumPy array to validate
        expected_dimensions: Expected number of dimensions (2 or 3)
        
    Returns:
        numpy.ndarray: Validated and potentially converted array
        
    Raises:
        ValueError: If data validation fails
    """
    if not isinstance(data, np.ndarray):
        raise ValueError("Data must be NumPy array")
    
    if len(data.shape) != expected_dimensions:
        raise ValueError(f"Expected {expected_dimensions}D array, got {len(data.shape)}D")
    
    if data.dtype not in [np.float32, np.float64]:
        print(f"Warning: Converting {data.dtype} to float32 for GPU compatibility")
        data = data.astype(np.float32)
    
    # Check for reasonable array sizes
    if expected_dimensions == 2:
        if data.shape[0] < 1 or data.shape[1] < 1:
            raise ValueError("2D array must have positive dimensions")
        print(f"Validated 2D NumPy array: {data.shape[0]} traces × {data.shape[1]} samples")
    elif expected_dimensions == 3:
        if data.shape[0] < 1 or data.shape[1] < 1 or data.shape[2] < 1:
            raise ValueError("3D array must have positive dimensions")
        print(f"Validated 3D NumPy array: {data.shape[0]} inlines × {data.shape[1]} crosslines × {data.shape[2]} samples")
    
    return data

class NumpySeismicData:
    """Base class for NumPy-based seismic data handling."""
    
    def __init__(self, data):
        """Initialize with NumPy array or file path."""
        if isinstance(data, str):
            self._data = np.load(data)
        elif isinstance(data, np.ndarray):
            self._data = data
        else:
            raise ValueError("Data must be NumPy array or file path string")
    
    def make_axis_divisible_by(self, factor):
        """Crop array dimensions to be divisible by factor for GPU optimization."""
        if len(self._data.shape) == 2:
            xlim = self._data.shape[0] // int(factor) * int(factor)
            ylim = self._data.shape[1] // int(factor) * int(factor)
            self._data = self._data[:xlim, :ylim]
        elif len(self._data.shape) == 3:
            xlim = self._data.shape[0] // int(factor) * int(factor)
            ylim = self._data.shape[1] // int(factor) * int(factor)
            self._data = self._data[:xlim, :ylim, :]
    
    def get_format(self):
        """Return data format string."""
        return "NUMPY"
    
    def get_vm(self):
        """Get visualization threshold (95th percentile)."""
        return self.vm

class Numpy2D(NumpySeismicData):
    """
    2D NumPy seismic data handler compatible with current architecture.
    
    Supports both file paths and direct NumPy arrays.
    Expected shape: (n_traces, n_samples)
    """
    
    def __init__(self, data):
        super().__init__(data)
        self._data = validate_numpy_data(self._data, 2)
        
        # Calculate visualization threshold
        self.vm = np.percentile(self._data, 95)
        
        # Default sampling rate for NumPy data (can be overridden)
        self._sample_rate = 0.001  # 1 ms default
    
    def get_iline(self):
        """Return the full 2D data array."""
        return self._data
    
    def get_n_xlines(self):
        """Return number of traces (first dimension)."""
        return self._data.shape[0]
    
    def get_n_zslices(self):
        """Return number of time samples (second dimension)."""
        return self._data.shape[1]
    
    def get_sample_rate(self):
        """Return sampling rate in seconds."""
        return self._sample_rate
    
    def set_sample_rate(self, dt):
        """Set sampling rate in seconds."""
        self._sample_rate = float(dt)
    
    def get_dimension(self):
        """Return dimension string."""
        return "2D"
    
    def get_data(self):
        """Return the underlying NumPy array."""
        return self._data

class Numpy3D(NumpySeismicData):
    """
    3D NumPy seismic data handler compatible with current architecture.
    
    Supports both file paths and direct NumPy arrays.
    Expected shape: (n_inlines, n_crosslines, n_samples)
    """
    
    def __init__(self, data):
        super().__init__(data)
        self._data = validate_numpy_data(self._data, 3)
        
        # Calculate visualization threshold using random sampling
        if self._data is not None:
            n_slices = min(10, self._data.shape[0])  # Don't exceed available inlines
            if n_slices > 0:
                import random
                slice_indices = [random.randint(0, self._data.shape[0]-1) for _ in range(n_slices)]
                sample_slices = [self.get_iline(idx) for idx in slice_indices]
                self.vm = np.percentile(sample_slices, 95)
            else:
                self.vm = np.percentile(self._data, 95)
        
        # Default sampling rate for NumPy data (can be overridden)
        self._sample_rate = 0.001  # 1 ms default
    
    def get_iline(self, indx):
        """Get inline slice at index."""
        return self._data[indx, :, :]
    
    def get_xline(self, indx):
        """Get crossline slice at index."""
        return self._data[:, indx, :]
    
    def get_zslice(self, indx):
        """Get time/depth slice at index."""
        return self._data[:, :, indx]
    
    def get_n_ilines(self):
        """Return number of inlines."""
        return self._data.shape[0]
    
    def get_n_xlines(self):
        """Return number of crosslines."""
        return self._data.shape[1]
    
    def get_n_zslices(self):
        """Return number of time samples."""
        return self._data.shape[2]
    
    def get_sample_rate(self):
        """Return sampling rate in seconds."""
        return self._sample_rate
    
    def set_sample_rate(self, dt):
        """Set sampling rate in seconds."""
        self._sample_rate = float(dt)
    
    def get_dimension(self):
        """Return dimension string."""
        return "3D"
    
    def get_cube(self):
        """Return the complete 3D array."""
        return self._data
    
    def get_data(self):
        """Return the underlying NumPy array."""
        return self._data

class NumpyHeaderLoader:
    """
    Header loader equivalent for NumPy data to maintain compatibility with existing workflow.
    
    Since NumPy arrays don't have embedded headers, this class provides
    reasonable defaults and allows coordinate system integration.
    """
    
    def __init__(self, numpy_data, dimension="3D"):
        """
        Initialize with NumPy data object.
        
        Args:
            numpy_data: Numpy2D or Numpy3D object
            dimension: "2D" or "3D"
        """
        self.numpy_data = numpy_data
        self.dimension = dimension
        self.source_file_path = "numpy_data"  # Placeholder for compatibility
        
        # Generate synthetic coordinate system
        if dimension == "3D":
            n_inlines = numpy_data.get_n_ilines()
            n_crosslines = numpy_data.get_n_xlines()
            
            # Create synthetic inline/crossline grids
            self.inlines = np.arange(1, n_inlines + 1)
            self.crosslines = np.arange(1, n_crosslines + 1)
            
            # Create synthetic coordinate grids (can be overridden by user)
            il_grid, xl_grid = np.meshgrid(self.inlines, self.crosslines, indexing='ij')
            self.x_coords = il_grid.flatten() * 25.0  # 25m spacing default
            self.y_coords = xl_grid.flatten() * 25.0
            
            # Create unique indices (all traces are unique in NumPy data)
            self.unique_indices = np.arange(len(self.x_coords))
            
            # Store original ranges
            self.original_inline_min = 1
            self.original_inline_max = n_inlines
            self.original_xline_min = 1
            self.original_xline_max = n_crosslines
            
        elif dimension == "2D":
            n_traces = numpy_data.get_n_xlines()
            
            # Create synthetic trace numbering
            self.inlines = np.arange(1, n_traces + 1)
            self.crosslines = np.ones(n_traces)  # All crossline 1 for 2D
            
            # Create synthetic coordinates for 2D line
            self.x_coords = np.arange(n_traces) * 25.0  # 25m spacing default
            self.y_coords = np.zeros(n_traces)  # Straight line along X-axis
            
            # Create unique indices
            self.unique_indices = np.arange(n_traces)
            
            # Store original ranges
            self.original_inline_min = 1
            self.original_inline_max = n_traces
            self.original_xline_min = 1
            self.original_xline_max = 1
    
    def get_inline_crossline_range(self):
        """Get inline/crossline ranges for compatibility."""
        return {
            'inline_min': self.original_inline_min,
            'inline_max': self.original_inline_max,
            'xline_min': self.original_xline_min,
            'xline_max': self.original_xline_max
        }
    
    def set_coordinate_system(self, x_coords, y_coords):
        """
        Override synthetic coordinates with real coordinate system.
        
        Args:
            x_coords: Array of X coordinates
            y_coords: Array of Y coordinates
        """
        if len(x_coords) != len(self.x_coords) or len(y_coords) != len(self.y_coords):
            raise ValueError("Coordinate arrays must match data dimensions")
        
        self.x_coords = np.array(x_coords, dtype=np.float64)
        self.y_coords = np.array(y_coords, dtype=np.float64)

def load_numpy_data(file_buffer, dimension_type, sample_rate=None):
    """
    Load NumPy seismic data from uploaded file buffer.
    
    Args:
        file_buffer: Streamlit file buffer containing .npy data
        dimension_type: "2D" or "3D"
        sample_rate: Optional sampling rate in seconds
        
    Returns:
        tuple: (numpy_data_object, header_loader_equivalent)
    """
    try:
        # Load NumPy array from buffer
        import tempfile
        import os
        
        # Create temporary file to load NumPy data
        with tempfile.NamedTemporaryFile(delete=False, suffix=".npy") as tmp_file:
            tmp_file.write(file_buffer.getvalue())
            tmp_file_path = tmp_file.name
        
        try:
            # Create appropriate NumPy data object
            if dimension_type == "2D":
                numpy_data = Numpy2D(tmp_file_path)
            elif dimension_type == "3D":
                numpy_data = Numpy3D(tmp_file_path)
            else:
                raise ValueError("Dimension type must be '2D' or '3D'")
            
            # Set custom sample rate if provided
            if sample_rate is not None:
                numpy_data.set_sample_rate(sample_rate)
            
            # Create header loader equivalent
            header_loader = NumpyHeaderLoader(numpy_data, dimension_type)
            
            print(f"Successfully loaded {dimension_type} NumPy data with shape {numpy_data.get_data().shape}")
            print(f"Sample rate: {numpy_data.get_sample_rate()*1000:.2f} ms")
            
            return numpy_data, header_loader
            
        finally:
            # Clean up temporary file
            if os.path.exists(tmp_file_path):
                os.remove(tmp_file_path)
    
    except Exception as e:
        raise ValueError(f"Error loading NumPy data: {e}")

def export_to_numpy(data, output_path, format_type='3D'):
    """
    Export processed seismic data to NumPy format.
    
    Args:
        data: NumPy array containing seismic data
        output_path: Path for output .npy file
        format_type: '2D' or '3D' format specification
    """
    try:
        if format_type == '3D':
            # Save as (inline, crossline, time) array
            np.save(output_path, data)
            print(f"Exported 3D data to {output_path} with shape {data.shape}")
        elif format_type == '2D':
            # Save as (trace, time) array
            np.save(output_path, data)
            print(f"Exported 2D data to {output_path} with shape {data.shape}")
        else:
            raise ValueError("Format type must be '2D' or '3D'")
            
    except Exception as e:
        raise RuntimeError(f"Error exporting to NumPy format: {e}")
